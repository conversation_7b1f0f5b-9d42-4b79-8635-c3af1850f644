{"sources": [{"file": "F:/Code/ESP32_Project/IIC/sample_project/build/CMakeFiles/bootloader"}, {"file": "F:/Code/ESP32_Project/IIC/sample_project/build/CMakeFiles/bootloader.rule"}, {"file": "F:/Code/ESP32_Project/IIC/sample_project/build/CMakeFiles/bootloader-complete.rule"}, {"file": "F:/Code/ESP32_Project/IIC/sample_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule"}, {"file": "F:/Code/ESP32_Project/IIC/sample_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule"}, {"file": "F:/Code/ESP32_Project/IIC/sample_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule"}, {"file": "F:/Code/ESP32_Project/IIC/sample_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule"}, {"file": "F:/Code/ESP32_Project/IIC/sample_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule"}, {"file": "F:/Code/ESP32_Project/IIC/sample_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule"}, {"file": "F:/Code/ESP32_Project/IIC/sample_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule"}], "target": {"labels": ["bootloader"], "name": "bootloader"}}