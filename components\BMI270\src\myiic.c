#include "driver/gpio.h"        // 提供GPIO配置函数(gpio_config)和宏定义(GPIO_MODE_OUTPUT等)
#include "esp32/rom/ets_sys.h"  // 提供 ets_delay_us() 函数
#include "myiic.h"

// 宏定义和全局变量
#define SYS_IIC_SDA       (45)
#define SYS_IIC_SCL       (0)

void Delay_ms(uint16_t xms)
{
    int i=0;
    for(i=0;i<xms;i++)
    {
        ets_delay_us(1000);
    }
}
void IIC_W_SDA(uint8_t val)
{
    gpio_set_level(SYS_IIC_SDA, (val));
    ets_delay_us(4);
}
void IIC_W_SCL(uint8_t val)
{
    gpio_set_level(SYS_IIC_SCL, (val));
    ets_delay_us(4);
}
uint8_t IIC_R_SDA(void)
{
    uint8_t val;
    val = gpio_get_level(SYS_IIC_SDA);
    ets_delay_us(4);
    return val;
}
//IIC初始化
void iic_gpio_init(void)
{
    gpio_config_t IIC_GPIO_CONFIG;
    IIC_GPIO_CONFIG.intr_type = GPIO_INTR_DISABLE;
    IIC_GPIO_CONFIG.mode = GPIO_MODE_OUTPUT;
    IIC_GPIO_CONFIG.pin_bit_mask = (1ULL << SYS_IIC_SDA) | (1ULL << SYS_IIC_SCL);
    IIC_GPIO_CONFIG.pull_up_en = 1;
    IIC_GPIO_CONFIG.pull_down_en = 1;
    
    gpio_config(&IIC_GPIO_CONFIG);

    IIC_W_SDA(1);            // 把SYS_IIC_SDA输出高电平
    IIC_W_SCL(1);            // 把SYS_IIC_SCL输出高电平
}

//产生IIC起始信号
//产生IIC停止信号
void iic_start(void)
{
    IIC_W_SDA(1);
    IIC_W_SCL(1);
    IIC_W_SDA(0);
    IIC_W_SCL(0);
}	  
void iic_stop(void)
{
    IIC_W_SCL(0);
    IIC_W_SDA(0);
    IIC_W_SCL(1);
    IIC_W_SDA(1);
}

//IIC发送一个字节
//IIC读一个字节
void iic_send_byte(uint8_t byte)
{                        
    uint8_t t;   
    IIC_W_SCL(0);
    for(t=0;t<8;t++)
    {         
        IIC_W_SDA(byte & (0x80 >> t));     
		IIC_W_SCL(1);
		IIC_W_SCL(0);
    }	 
} 	    
uint8_t iic_read_byte(void)
{
	uint8_t i;
    uint8_t receive=0x00;
    IIC_W_SDA(1);
    for(i=0;i<8;i++ )
	{
        IIC_W_SCL(1);
        if(IIC_R_SDA() == 1)
            {receive |= (0x80 >> i);}
        IIC_W_SCL(0);
    }
    return receive;
}

//IIC发送一个应答
//IIC接收一个应答
void iic_send_ack(uint8_t ack)
{                        
    IIC_W_SDA(ack);     
    IIC_W_SCL(1);
    IIC_W_SCL(0);
} 	    
uint8_t iic_read_ack(void)
{
	uint8_t ack;
    IIC_W_SDA(1);
    IIC_W_SCL(1);
    ack = IIC_R_SDA();
    IIC_W_SCL(0);
    return ack;
}


