//#ifndef _MYIIC_H
//#define _MYIIC_H
#pragma once

#include "stdint.h"

void iic_gpio_init(void);           //IIC引脚初始化
void iic_start(void);               //产生IIC起始信号
void iic_stop(void);                //产生IIC停止信号
void iic_send_byte(uint8_t byte);   //IIC发送一个字节
uint8_t iic_read_byte(void);        //IIC读一个字节
void iic_send_ack(uint8_t ack);     //IIC发送一个应答
uint8_t iic_read_ack(void);         //IIC接收一个应答



//#endif

