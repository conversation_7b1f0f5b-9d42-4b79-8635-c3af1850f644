CMakeFiles/sample_project.elf.dir/project_elf_src_esp32s3.c.obj -LD:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/soc/esp32s3/ld   -LD:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_rom/esp32s3/ld   -LF:/Code/ESP32_Project/IIC/sample_project/build/esp-idf/esp_system/ld   -LD:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_phy/lib/esp32s3   -LD:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3 esp-idf/xtensa/libxtensa.a  esp-idf/esp_driver_gpio/libesp_driver_gpio.a  esp-idf/esp_pm/libesp_pm.a  esp-idf/mbedtls/libmbedtls.a  esp-idf/esp_app_format/libesp_app_format.a  esp-idf/esp_bootloader_format/libesp_bootloader_format.a  esp-idf/app_update/libapp_update.a  esp-idf/esp_partition/libesp_partition.a  esp-idf/efuse/libefuse.a  esp-idf/bootloader_support/libbootloader_support.a  esp-idf/esp_mm/libesp_mm.a  esp-idf/spi_flash/libspi_flash.a  esp-idf/esp_system/libesp_system.a  esp-idf/esp_common/libesp_common.a  esp-idf/esp_rom/libesp_rom.a  esp-idf/hal/libhal.a  esp-idf/log/liblog.a  esp-idf/heap/libheap.a  esp-idf/soc/libsoc.a  esp-idf/esp_hw_support/libesp_hw_support.a  esp-idf/freertos/libfreertos.a  esp-idf/newlib/libnewlib.a  esp-idf/pthread/libpthread.a  esp-idf/cxx/libcxx.a  esp-idf/esp_timer/libesp_timer.a  esp-idf/esp_driver_gptimer/libesp_driver_gptimer.a  esp-idf/esp_ringbuf/libesp_ringbuf.a  esp-idf/esp_driver_uart/libesp_driver_uart.a  esp-idf/app_trace/libapp_trace.a  esp-idf/esp_event/libesp_event.a  esp-idf/nvs_flash/libnvs_flash.a  esp-idf/esp_driver_pcnt/libesp_driver_pcnt.a  esp-idf/esp_driver_spi/libesp_driver_spi.a  esp-idf/esp_driver_mcpwm/libesp_driver_mcpwm.a  esp-idf/esp_driver_i2s/libesp_driver_i2s.a  esp-idf/sdmmc/libsdmmc.a  esp-idf/esp_driver_sdmmc/libesp_driver_sdmmc.a  esp-idf/esp_driver_sdspi/libesp_driver_sdspi.a  esp-idf/esp_driver_rmt/libesp_driver_rmt.a  esp-idf/esp_driver_tsens/libesp_driver_tsens.a  esp-idf/esp_driver_sdm/libesp_driver_sdm.a  esp-idf/esp_driver_i2c/libesp_driver_i2c.a  esp-idf/esp_driver_ledc/libesp_driver_ledc.a  esp-idf/esp_driver_usb_serial_jtag/libesp_driver_usb_serial_jtag.a  esp-idf/driver/libdriver.a  esp-idf/esp_phy/libesp_phy.a  esp-idf/esp_vfs_console/libesp_vfs_console.a  esp-idf/vfs/libvfs.a  esp-idf/lwip/liblwip.a  esp-idf/esp_netif/libesp_netif.a  esp-idf/wpa_supplicant/libwpa_supplicant.a  esp-idf/esp_coex/libesp_coex.a  esp-idf/esp_wifi/libesp_wifi.a  esp-idf/unity/libunity.a  esp-idf/cmock/libcmock.a  esp-idf/console/libconsole.a  esp-idf/http_parser/libhttp_parser.a  esp-idf/esp-tls/libesp-tls.a  esp-idf/esp_adc/libesp_adc.a  esp-idf/esp_driver_cam/libesp_driver_cam.a  esp-idf/esp_eth/libesp_eth.a  esp-idf/esp_gdbstub/libesp_gdbstub.a  esp-idf/esp_hid/libesp_hid.a  esp-idf/tcp_transport/libtcp_transport.a  esp-idf/esp_http_client/libesp_http_client.a  esp-idf/esp_http_server/libesp_http_server.a  esp-idf/esp_https_ota/libesp_https_ota.a  esp-idf/esp_https_server/libesp_https_server.a  esp-idf/esp_lcd/libesp_lcd.a  esp-idf/protobuf-c/libprotobuf-c.a  esp-idf/protocomm/libprotocomm.a  esp-idf/esp_local_ctrl/libesp_local_ctrl.a  esp-idf/espcoredump/libespcoredump.a  esp-idf/wear_levelling/libwear_levelling.a  esp-idf/fatfs/libfatfs.a  esp-idf/json/libjson.a  esp-idf/mqtt/libmqtt.a  esp-idf/nvs_sec_provider/libnvs_sec_provider.a  esp-idf/perfmon/libperfmon.a  esp-idf/spiffs/libspiffs.a  esp-idf/touch_element/libtouch_element.a  esp-idf/usb/libusb.a  esp-idf/wifi_provisioning/libwifi_provisioning.a  esp-idf/main/libmain.a  esp-idf/app_trace/libapp_trace.a  esp-idf/app_trace/libapp_trace.a  esp-idf/cmock/libcmock.a  esp-idf/unity/libunity.a  esp-idf/esp_driver_cam/libesp_driver_cam.a  esp-idf/esp_hid/libesp_hid.a  esp-idf/esp_lcd/libesp_lcd.a  esp-idf/esp_local_ctrl/libesp_local_ctrl.a  esp-idf/esp_https_server/libesp_https_server.a  esp-idf/espcoredump/libespcoredump.a  -u esp_system_include_coredump_init  esp-idf/fatfs/libfatfs.a  esp-idf/wear_levelling/libwear_levelling.a  esp-idf/mqtt/libmqtt.a  esp-idf/nvs_sec_provider/libnvs_sec_provider.a  -u nvs_sec_provider_include_impl  esp-idf/perfmon/libperfmon.a  esp-idf/spiffs/libspiffs.a  esp-idf/touch_element/libtouch_element.a  esp-idf/usb/libusb.a  esp-idf/wifi_provisioning/libwifi_provisioning.a  esp-idf/protocomm/libprotocomm.a  esp-idf/console/libconsole.a  esp-idf/protobuf-c/libprotobuf-c.a  esp-idf/json/libjson.a  esp-idf/xtensa/libxtensa.a  esp-idf/esp_driver_gpio/libesp_driver_gpio.a  esp-idf/esp_pm/libesp_pm.a  esp-idf/mbedtls/libmbedtls.a  esp-idf/esp_app_format/libesp_app_format.a  esp-idf/esp_bootloader_format/libesp_bootloader_format.a  esp-idf/app_update/libapp_update.a  esp-idf/esp_partition/libesp_partition.a  esp-idf/efuse/libefuse.a  esp-idf/bootloader_support/libbootloader_support.a  esp-idf/esp_mm/libesp_mm.a  esp-idf/spi_flash/libspi_flash.a  esp-idf/esp_system/libesp_system.a  esp-idf/esp_common/libesp_common.a  esp-idf/esp_rom/libesp_rom.a  esp-idf/hal/libhal.a  esp-idf/log/liblog.a  esp-idf/heap/libheap.a  esp-idf/soc/libsoc.a  esp-idf/esp_hw_support/libesp_hw_support.a  esp-idf/freertos/libfreertos.a  esp-idf/newlib/libnewlib.a  esp-idf/pthread/libpthread.a  esp-idf/cxx/libcxx.a  esp-idf/esp_timer/libesp_timer.a  esp-idf/esp_driver_gptimer/libesp_driver_gptimer.a  esp-idf/esp_ringbuf/libesp_ringbuf.a  esp-idf/esp_driver_uart/libesp_driver_uart.a  esp-idf/esp_event/libesp_event.a  esp-idf/nvs_flash/libnvs_flash.a  esp-idf/esp_driver_pcnt/libesp_driver_pcnt.a  esp-idf/esp_driver_spi/libesp_driver_spi.a  esp-idf/esp_driver_mcpwm/libesp_driver_mcpwm.a  esp-idf/esp_driver_i2s/libesp_driver_i2s.a  esp-idf/sdmmc/libsdmmc.a  esp-idf/esp_driver_sdmmc/libesp_driver_sdmmc.a  esp-idf/esp_driver_sdspi/libesp_driver_sdspi.a  esp-idf/esp_driver_rmt/libesp_driver_rmt.a  esp-idf/esp_driver_tsens/libesp_driver_tsens.a  esp-idf/esp_driver_sdm/libesp_driver_sdm.a  esp-idf/esp_driver_i2c/libesp_driver_i2c.a  esp-idf/esp_driver_ledc/libesp_driver_ledc.a  esp-idf/esp_driver_usb_serial_jtag/libesp_driver_usb_serial_jtag.a  esp-idf/driver/libdriver.a  esp-idf/esp_phy/libesp_phy.a  esp-idf/esp_vfs_console/libesp_vfs_console.a  esp-idf/vfs/libvfs.a  esp-idf/lwip/liblwip.a  esp-idf/esp_netif/libesp_netif.a  esp-idf/wpa_supplicant/libwpa_supplicant.a  esp-idf/esp_coex/libesp_coex.a  esp-idf/esp_wifi/libesp_wifi.a  esp-idf/http_parser/libhttp_parser.a  esp-idf/esp-tls/libesp-tls.a  esp-idf/esp_adc/libesp_adc.a  esp-idf/esp_eth/libesp_eth.a  esp-idf/esp_gdbstub/libesp_gdbstub.a  esp-idf/tcp_transport/libtcp_transport.a  esp-idf/esp_http_client/libesp_http_client.a  esp-idf/esp_http_server/libesp_http_server.a  esp-idf/esp_https_ota/libesp_https_ota.a  esp-idf/mbedtls/mbedtls/library/libmbedtls.a  esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a  esp-idf/mbedtls/mbedtls/library/libmbedx509.a  esp-idf/mbedtls/mbedtls/3rdparty/everest/libeverest.a  esp-idf/mbedtls/mbedtls/3rdparty/p256-m/libp256m.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libcore.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libespnow.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libmesh.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libnet80211.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libpp.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libsmartconfig.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libwapi.a  esp-idf/xtensa/libxtensa.a  esp-idf/esp_driver_gpio/libesp_driver_gpio.a  esp-idf/esp_pm/libesp_pm.a  esp-idf/mbedtls/libmbedtls.a  esp-idf/esp_app_format/libesp_app_format.a  esp-idf/esp_bootloader_format/libesp_bootloader_format.a  esp-idf/app_update/libapp_update.a  esp-idf/esp_partition/libesp_partition.a  esp-idf/efuse/libefuse.a  esp-idf/bootloader_support/libbootloader_support.a  esp-idf/esp_mm/libesp_mm.a  esp-idf/spi_flash/libspi_flash.a  esp-idf/esp_system/libesp_system.a  esp-idf/esp_common/libesp_common.a  esp-idf/esp_rom/libesp_rom.a  esp-idf/hal/libhal.a  esp-idf/log/liblog.a  esp-idf/heap/libheap.a  esp-idf/soc/libsoc.a  esp-idf/esp_hw_support/libesp_hw_support.a  esp-idf/freertos/libfreertos.a  esp-idf/newlib/libnewlib.a  esp-idf/pthread/libpthread.a  esp-idf/cxx/libcxx.a  esp-idf/esp_timer/libesp_timer.a  esp-idf/esp_driver_gptimer/libesp_driver_gptimer.a  esp-idf/esp_ringbuf/libesp_ringbuf.a  esp-idf/esp_driver_uart/libesp_driver_uart.a  esp-idf/esp_event/libesp_event.a  esp-idf/nvs_flash/libnvs_flash.a  esp-idf/esp_driver_pcnt/libesp_driver_pcnt.a  esp-idf/esp_driver_spi/libesp_driver_spi.a  esp-idf/esp_driver_mcpwm/libesp_driver_mcpwm.a  esp-idf/esp_driver_i2s/libesp_driver_i2s.a  esp-idf/sdmmc/libsdmmc.a  esp-idf/esp_driver_sdmmc/libesp_driver_sdmmc.a  esp-idf/esp_driver_sdspi/libesp_driver_sdspi.a  esp-idf/esp_driver_rmt/libesp_driver_rmt.a  esp-idf/esp_driver_tsens/libesp_driver_tsens.a  esp-idf/esp_driver_sdm/libesp_driver_sdm.a  esp-idf/esp_driver_i2c/libesp_driver_i2c.a  esp-idf/esp_driver_ledc/libesp_driver_ledc.a  esp-idf/esp_driver_usb_serial_jtag/libesp_driver_usb_serial_jtag.a  esp-idf/driver/libdriver.a  esp-idf/esp_phy/libesp_phy.a  esp-idf/esp_vfs_console/libesp_vfs_console.a  esp-idf/vfs/libvfs.a  esp-idf/lwip/liblwip.a  esp-idf/esp_netif/libesp_netif.a  esp-idf/wpa_supplicant/libwpa_supplicant.a  esp-idf/esp_coex/libesp_coex.a  esp-idf/esp_wifi/libesp_wifi.a  esp-idf/http_parser/libhttp_parser.a  esp-idf/esp-tls/libesp-tls.a  esp-idf/esp_adc/libesp_adc.a  esp-idf/esp_eth/libesp_eth.a  esp-idf/esp_gdbstub/libesp_gdbstub.a  esp-idf/tcp_transport/libtcp_transport.a  esp-idf/esp_http_client/libesp_http_client.a  esp-idf/esp_http_server/libesp_http_server.a  esp-idf/esp_https_ota/libesp_https_ota.a  esp-idf/mbedtls/mbedtls/library/libmbedtls.a  esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a  esp-idf/mbedtls/mbedtls/library/libmbedx509.a  esp-idf/mbedtls/mbedtls/3rdparty/everest/libeverest.a  esp-idf/mbedtls/mbedtls/3rdparty/p256-m/libp256m.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libcore.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libespnow.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libmesh.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libnet80211.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libpp.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libsmartconfig.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libwapi.a  esp-idf/xtensa/libxtensa.a  esp-idf/esp_driver_gpio/libesp_driver_gpio.a  esp-idf/esp_pm/libesp_pm.a  esp-idf/mbedtls/libmbedtls.a  esp-idf/esp_app_format/libesp_app_format.a  esp-idf/esp_bootloader_format/libesp_bootloader_format.a  esp-idf/app_update/libapp_update.a  esp-idf/esp_partition/libesp_partition.a  esp-idf/efuse/libefuse.a  esp-idf/bootloader_support/libbootloader_support.a  esp-idf/esp_mm/libesp_mm.a  esp-idf/spi_flash/libspi_flash.a  esp-idf/esp_system/libesp_system.a  esp-idf/esp_common/libesp_common.a  esp-idf/esp_rom/libesp_rom.a  esp-idf/hal/libhal.a  esp-idf/log/liblog.a  esp-idf/heap/libheap.a  esp-idf/soc/libsoc.a  esp-idf/esp_hw_support/libesp_hw_support.a  esp-idf/freertos/libfreertos.a  esp-idf/newlib/libnewlib.a  esp-idf/pthread/libpthread.a  esp-idf/cxx/libcxx.a  esp-idf/esp_timer/libesp_timer.a  esp-idf/esp_driver_gptimer/libesp_driver_gptimer.a  esp-idf/esp_ringbuf/libesp_ringbuf.a  esp-idf/esp_driver_uart/libesp_driver_uart.a  esp-idf/esp_event/libesp_event.a  esp-idf/nvs_flash/libnvs_flash.a  esp-idf/esp_driver_pcnt/libesp_driver_pcnt.a  esp-idf/esp_driver_spi/libesp_driver_spi.a  esp-idf/esp_driver_mcpwm/libesp_driver_mcpwm.a  esp-idf/esp_driver_i2s/libesp_driver_i2s.a  esp-idf/sdmmc/libsdmmc.a  esp-idf/esp_driver_sdmmc/libesp_driver_sdmmc.a  esp-idf/esp_driver_sdspi/libesp_driver_sdspi.a  esp-idf/esp_driver_rmt/libesp_driver_rmt.a  esp-idf/esp_driver_tsens/libesp_driver_tsens.a  esp-idf/esp_driver_sdm/libesp_driver_sdm.a  esp-idf/esp_driver_i2c/libesp_driver_i2c.a  esp-idf/esp_driver_ledc/libesp_driver_ledc.a  esp-idf/esp_driver_usb_serial_jtag/libesp_driver_usb_serial_jtag.a  esp-idf/driver/libdriver.a  esp-idf/esp_phy/libesp_phy.a  esp-idf/esp_vfs_console/libesp_vfs_console.a  esp-idf/vfs/libvfs.a  esp-idf/lwip/liblwip.a  esp-idf/esp_netif/libesp_netif.a  esp-idf/wpa_supplicant/libwpa_supplicant.a  esp-idf/esp_coex/libesp_coex.a  esp-idf/esp_wifi/libesp_wifi.a  esp-idf/http_parser/libhttp_parser.a  esp-idf/esp-tls/libesp-tls.a  esp-idf/esp_adc/libesp_adc.a  esp-idf/esp_eth/libesp_eth.a  esp-idf/esp_gdbstub/libesp_gdbstub.a  esp-idf/tcp_transport/libtcp_transport.a  esp-idf/esp_http_client/libesp_http_client.a  esp-idf/esp_http_server/libesp_http_server.a  esp-idf/esp_https_ota/libesp_https_ota.a  esp-idf/mbedtls/mbedtls/library/libmbedtls.a  esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a  esp-idf/mbedtls/mbedtls/library/libmbedx509.a  esp-idf/mbedtls/mbedtls/3rdparty/everest/libeverest.a  esp-idf/mbedtls/mbedtls/3rdparty/p256-m/libp256m.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libcore.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libespnow.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libmesh.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libnet80211.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libpp.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libsmartconfig.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libwapi.a  esp-idf/xtensa/libxtensa.a  esp-idf/esp_driver_gpio/libesp_driver_gpio.a  esp-idf/esp_pm/libesp_pm.a  esp-idf/mbedtls/libmbedtls.a  esp-idf/esp_app_format/libesp_app_format.a  esp-idf/esp_bootloader_format/libesp_bootloader_format.a  esp-idf/app_update/libapp_update.a  esp-idf/esp_partition/libesp_partition.a  esp-idf/efuse/libefuse.a  esp-idf/bootloader_support/libbootloader_support.a  esp-idf/esp_mm/libesp_mm.a  esp-idf/spi_flash/libspi_flash.a  esp-idf/esp_system/libesp_system.a  esp-idf/esp_common/libesp_common.a  esp-idf/esp_rom/libesp_rom.a  esp-idf/hal/libhal.a  esp-idf/log/liblog.a  esp-idf/heap/libheap.a  esp-idf/soc/libsoc.a  esp-idf/esp_hw_support/libesp_hw_support.a  esp-idf/freertos/libfreertos.a  esp-idf/newlib/libnewlib.a  esp-idf/pthread/libpthread.a  esp-idf/cxx/libcxx.a  esp-idf/esp_timer/libesp_timer.a  esp-idf/esp_driver_gptimer/libesp_driver_gptimer.a  esp-idf/esp_ringbuf/libesp_ringbuf.a  esp-idf/esp_driver_uart/libesp_driver_uart.a  esp-idf/esp_event/libesp_event.a  esp-idf/nvs_flash/libnvs_flash.a  esp-idf/esp_driver_pcnt/libesp_driver_pcnt.a  esp-idf/esp_driver_spi/libesp_driver_spi.a  esp-idf/esp_driver_mcpwm/libesp_driver_mcpwm.a  esp-idf/esp_driver_i2s/libesp_driver_i2s.a  esp-idf/sdmmc/libsdmmc.a  esp-idf/esp_driver_sdmmc/libesp_driver_sdmmc.a  esp-idf/esp_driver_sdspi/libesp_driver_sdspi.a  esp-idf/esp_driver_rmt/libesp_driver_rmt.a  esp-idf/esp_driver_tsens/libesp_driver_tsens.a  esp-idf/esp_driver_sdm/libesp_driver_sdm.a  esp-idf/esp_driver_i2c/libesp_driver_i2c.a  esp-idf/esp_driver_ledc/libesp_driver_ledc.a  esp-idf/esp_driver_usb_serial_jtag/libesp_driver_usb_serial_jtag.a  esp-idf/driver/libdriver.a  esp-idf/esp_phy/libesp_phy.a  esp-idf/esp_vfs_console/libesp_vfs_console.a  esp-idf/vfs/libvfs.a  esp-idf/lwip/liblwip.a  esp-idf/esp_netif/libesp_netif.a  esp-idf/wpa_supplicant/libwpa_supplicant.a  esp-idf/esp_coex/libesp_coex.a  esp-idf/esp_wifi/libesp_wifi.a  esp-idf/http_parser/libhttp_parser.a  esp-idf/esp-tls/libesp-tls.a  esp-idf/esp_adc/libesp_adc.a  esp-idf/esp_eth/libesp_eth.a  esp-idf/esp_gdbstub/libesp_gdbstub.a  esp-idf/tcp_transport/libtcp_transport.a  esp-idf/esp_http_client/libesp_http_client.a  esp-idf/esp_http_server/libesp_http_server.a  esp-idf/esp_https_ota/libesp_https_ota.a  esp-idf/mbedtls/mbedtls/library/libmbedtls.a  esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a  esp-idf/mbedtls/mbedtls/library/libmbedx509.a  esp-idf/mbedtls/mbedtls/3rdparty/everest/libeverest.a  esp-idf/mbedtls/mbedtls/3rdparty/p256-m/libp256m.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libcore.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libespnow.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libmesh.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libnet80211.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libpp.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libsmartconfig.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libwapi.a  esp-idf/xtensa/libxtensa.a  esp-idf/esp_driver_gpio/libesp_driver_gpio.a  esp-idf/esp_pm/libesp_pm.a  esp-idf/mbedtls/libmbedtls.a  esp-idf/esp_app_format/libesp_app_format.a  esp-idf/esp_bootloader_format/libesp_bootloader_format.a  esp-idf/app_update/libapp_update.a  esp-idf/esp_partition/libesp_partition.a  esp-idf/efuse/libefuse.a  esp-idf/bootloader_support/libbootloader_support.a  esp-idf/esp_mm/libesp_mm.a  esp-idf/spi_flash/libspi_flash.a  esp-idf/esp_system/libesp_system.a  esp-idf/esp_common/libesp_common.a  esp-idf/esp_rom/libesp_rom.a  esp-idf/hal/libhal.a  esp-idf/log/liblog.a  esp-idf/heap/libheap.a  esp-idf/soc/libsoc.a  esp-idf/esp_hw_support/libesp_hw_support.a  esp-idf/freertos/libfreertos.a  esp-idf/newlib/libnewlib.a  esp-idf/pthread/libpthread.a  esp-idf/cxx/libcxx.a  esp-idf/esp_timer/libesp_timer.a  esp-idf/esp_driver_gptimer/libesp_driver_gptimer.a  esp-idf/esp_ringbuf/libesp_ringbuf.a  esp-idf/esp_driver_uart/libesp_driver_uart.a  esp-idf/esp_event/libesp_event.a  esp-idf/nvs_flash/libnvs_flash.a  esp-idf/esp_driver_pcnt/libesp_driver_pcnt.a  esp-idf/esp_driver_spi/libesp_driver_spi.a  esp-idf/esp_driver_mcpwm/libesp_driver_mcpwm.a  esp-idf/esp_driver_i2s/libesp_driver_i2s.a  esp-idf/sdmmc/libsdmmc.a  esp-idf/esp_driver_sdmmc/libesp_driver_sdmmc.a  esp-idf/esp_driver_sdspi/libesp_driver_sdspi.a  esp-idf/esp_driver_rmt/libesp_driver_rmt.a  esp-idf/esp_driver_tsens/libesp_driver_tsens.a  esp-idf/esp_driver_sdm/libesp_driver_sdm.a  esp-idf/esp_driver_i2c/libesp_driver_i2c.a  esp-idf/esp_driver_ledc/libesp_driver_ledc.a  esp-idf/esp_driver_usb_serial_jtag/libesp_driver_usb_serial_jtag.a  esp-idf/driver/libdriver.a  esp-idf/esp_phy/libesp_phy.a  esp-idf/esp_vfs_console/libesp_vfs_console.a  esp-idf/vfs/libvfs.a  esp-idf/lwip/liblwip.a  esp-idf/esp_netif/libesp_netif.a  esp-idf/wpa_supplicant/libwpa_supplicant.a  esp-idf/esp_coex/libesp_coex.a  esp-idf/esp_wifi/libesp_wifi.a  esp-idf/http_parser/libhttp_parser.a  esp-idf/esp-tls/libesp-tls.a  esp-idf/esp_adc/libesp_adc.a  esp-idf/esp_eth/libesp_eth.a  esp-idf/esp_gdbstub/libesp_gdbstub.a  esp-idf/tcp_transport/libtcp_transport.a  esp-idf/esp_http_client/libesp_http_client.a  esp-idf/esp_http_server/libesp_http_server.a  esp-idf/esp_https_ota/libesp_https_ota.a  esp-idf/mbedtls/mbedtls/library/libmbedtls.a  esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a  esp-idf/mbedtls/mbedtls/library/libmbedx509.a  esp-idf/mbedtls/mbedtls/3rdparty/everest/libeverest.a  esp-idf/mbedtls/mbedtls/3rdparty/p256-m/libp256m.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libcore.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libespnow.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libmesh.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libnet80211.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libpp.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libsmartconfig.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libwapi.a  esp-idf/xtensa/libxtensa.a  esp-idf/esp_driver_gpio/libesp_driver_gpio.a  esp-idf/esp_pm/libesp_pm.a  esp-idf/mbedtls/libmbedtls.a  esp-idf/esp_app_format/libesp_app_format.a  esp-idf/esp_bootloader_format/libesp_bootloader_format.a  esp-idf/app_update/libapp_update.a  esp-idf/esp_partition/libesp_partition.a  esp-idf/efuse/libefuse.a  esp-idf/bootloader_support/libbootloader_support.a  esp-idf/esp_mm/libesp_mm.a  esp-idf/spi_flash/libspi_flash.a  esp-idf/esp_system/libesp_system.a  esp-idf/esp_common/libesp_common.a  esp-idf/esp_rom/libesp_rom.a  esp-idf/hal/libhal.a  esp-idf/log/liblog.a  esp-idf/heap/libheap.a  esp-idf/soc/libsoc.a  esp-idf/esp_hw_support/libesp_hw_support.a  esp-idf/freertos/libfreertos.a  esp-idf/newlib/libnewlib.a  esp-idf/pthread/libpthread.a  esp-idf/cxx/libcxx.a  esp-idf/esp_timer/libesp_timer.a  esp-idf/esp_driver_gptimer/libesp_driver_gptimer.a  esp-idf/esp_ringbuf/libesp_ringbuf.a  esp-idf/esp_driver_uart/libesp_driver_uart.a  esp-idf/esp_event/libesp_event.a  esp-idf/nvs_flash/libnvs_flash.a  esp-idf/esp_driver_pcnt/libesp_driver_pcnt.a  esp-idf/esp_driver_spi/libesp_driver_spi.a  esp-idf/esp_driver_mcpwm/libesp_driver_mcpwm.a  esp-idf/esp_driver_i2s/libesp_driver_i2s.a  esp-idf/sdmmc/libsdmmc.a  esp-idf/esp_driver_sdmmc/libesp_driver_sdmmc.a  esp-idf/esp_driver_sdspi/libesp_driver_sdspi.a  esp-idf/esp_driver_rmt/libesp_driver_rmt.a  esp-idf/esp_driver_tsens/libesp_driver_tsens.a  esp-idf/esp_driver_sdm/libesp_driver_sdm.a  esp-idf/esp_driver_i2c/libesp_driver_i2c.a  esp-idf/esp_driver_ledc/libesp_driver_ledc.a  esp-idf/esp_driver_usb_serial_jtag/libesp_driver_usb_serial_jtag.a  esp-idf/driver/libdriver.a  esp-idf/esp_phy/libesp_phy.a  esp-idf/esp_vfs_console/libesp_vfs_console.a  esp-idf/vfs/libvfs.a  esp-idf/lwip/liblwip.a  esp-idf/esp_netif/libesp_netif.a  esp-idf/wpa_supplicant/libwpa_supplicant.a  esp-idf/esp_coex/libesp_coex.a  esp-idf/esp_wifi/libesp_wifi.a  esp-idf/http_parser/libhttp_parser.a  esp-idf/esp-tls/libesp-tls.a  esp-idf/esp_adc/libesp_adc.a  esp-idf/esp_eth/libesp_eth.a  esp-idf/esp_gdbstub/libesp_gdbstub.a  esp-idf/tcp_transport/libtcp_transport.a  esp-idf/esp_http_client/libesp_http_client.a  esp-idf/esp_http_server/libesp_http_server.a  esp-idf/esp_https_ota/libesp_https_ota.a  esp-idf/mbedtls/mbedtls/library/libmbedtls.a  esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a  esp-idf/mbedtls/mbedtls/library/libmbedx509.a  esp-idf/mbedtls/mbedtls/3rdparty/everest/libeverest.a  esp-idf/mbedtls/mbedtls/3rdparty/p256-m/libp256m.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libcore.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libespnow.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libmesh.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libnet80211.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libpp.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libsmartconfig.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/lib/esp32s3/libwapi.a  D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/xtensa/esp32s3/libxt_hal.a  -u esp_app_desc  -u esp_efuse_startup_include_func  -u ld_include_highint_hdl  -u start_app  -u start_app_other_cores  -u __ubsan_include  -u esp_system_include_startup_funcs  -Wl,--wrap=longjmp  -u __assert_func  -Wl,--undefined=FreeRTOS_openocd_params  -u app_main  -lc  -lm  -u newlib_include_heap_impl  -u newlib_include_syscalls_impl  -u newlib_include_pthread_impl  -u newlib_include_assert_impl  -u newlib_include_init_funcs  -u pthread_include_pthread_impl  -u pthread_include_pthread_cond_var_impl  -u pthread_include_pthread_local_storage_impl  -u pthread_include_pthread_rwlock_impl  -u pthread_include_pthread_semaphore_impl  -Wl,--wrap=__register_frame_info_bases  -Wl,--wrap=__register_frame_info  -Wl,--wrap=__register_frame  -Wl,--wrap=__register_frame_info_table_bases  -Wl,--wrap=__register_frame_info_table  -Wl,--wrap=__register_frame_table  -Wl,--wrap=__deregister_frame_info_bases  -Wl,--wrap=__deregister_frame_info  -Wl,--wrap=_Unwind_Find_FDE  -Wl,--wrap=_Unwind_GetGR  -Wl,--wrap=_Unwind_GetCFA  -Wl,--wrap=_Unwind_GetIP  -Wl,--wrap=_Unwind_GetIPInfo  -Wl,--wrap=_Unwind_GetRegionStart  -Wl,--wrap=_Unwind_GetDataRelBase  -Wl,--wrap=_Unwind_GetTextRelBase  -Wl,--wrap=_Unwind_SetIP  -Wl,--wrap=_Unwind_SetGR  -Wl,--wrap=_Unwind_GetLanguageSpecificData  -Wl,--wrap=_Unwind_FindEnclosingFunction  -Wl,--wrap=_Unwind_Resume  -Wl,--wrap=_Unwind_RaiseException  -Wl,--wrap=_Unwind_DeleteException  -Wl,--wrap=_Unwind_ForcedUnwind  -Wl,--wrap=_Unwind_Resume_or_Rethrow  -Wl,--wrap=_Unwind_Backtrace  -Wl,--wrap=__cxa_call_unexpected  -Wl,--wrap=__gxx_personality_v0  -Wl,--wrap=__cxa_throw  -Wl,--wrap=__cxa_allocate_exception  -u __cxa_guard_dummy  -u __cxx_init_dummy  -lstdc++  esp-idf/pthread/libpthread.a  esp-idf/newlib/libnewlib.a  -lgcc  esp-idf/cxx/libcxx.a  -u __cxx_fatal_exception  -u esp_timer_init_include_func  -u uart_vfs_include_dev_init  -u usb_serial_jtag_vfs_include_dev_init  -u usb_serial_jtag_connection_monitor_include  -u include_esp_phy_override  -lphy  -lbtbb  esp-idf/esp_phy/libesp_phy.a  -lphy  -lbtbb  esp-idf/esp_phy/libesp_phy.a  -lphy  -lbtbb  -u esp_vfs_include_console_register  -u vfs_include_syscalls_impl